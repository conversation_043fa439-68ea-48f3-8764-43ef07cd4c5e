# Build stage
FROM golang:1.24-alpine AS builder

WORKDIR /app

# Copy go mod and sum files
COPY go.mod go.sum ./

# Download dependencies
RUN go mod download

# Copy source code
COPY . .

# Build the application
RUN CGO_ENABLED=0 GOOS=linux go build -o bookstore-api

# Runtime stage
FROM alpine:latest

WORKDIR /app

# Copy binary from builder
COPY --from=builder /app/bookstore-api .

# Copy .env file (for local development)
# Note: For production, use proper secrets management
COPY .env .

# Expose port
EXPOSE 8080

# Command to run the application
CMD ["./bookstore-api"]